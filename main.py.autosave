import time
import os
import sys
import struct
from media.sensor import *
from media.display import *
from media.media import *
from time import ticks_ms
from machine import UART
from machine import FPIOA
from machine import Pin
from machine import PWM
from machine import Timer

# 导入单目视觉测量模块
from mono_measure import MonoMeasure
from uart_protocol import UartProtocol
from display_ui import DisplayUI

# ==================== 全局配置参数 ====================
# 可在此处直接修改系统参数，无需深入代码内部

# 1. 摄像头和显示配置
SENSOR_WIDTH = 1280         # 传感器初始宽度
SENSOR_HEIGHT = 960         # 传感器初始高度
FRAME_WIDTH = 640           # 输出帧宽度
FRAME_HEIGHT = 480          # 输出帧高度

# 2. 标定参数配置
CALIBRATION_DISTANCE = 150.0    # 标定距离(cm) - 可修改为50-200cm之间的任意值

# 3. A4纸检测参数
MIN_A4_AREA = 5000          # A4纸最小面积阈值(像素²) - 过滤小噪声
MAX_A4_AREA = 30000        # A4纸最大面积阈值(像素²) - 过滤过大区域
ASPECT_TOLERANCE = 0.35      # 长宽比容差 - 允许的A4纸形状偏差

# 4. 通信和定时器配置
UART_BAUDRATE = 115200      # 串口波特率
TIMER_PERIOD = 30           # 定时器周期(ms) - 数据发送频率

# 5. 按键消抖配置
BUTTON_DEBOUNCE_MS = 10     # 按键消抖延时(ms)

# 6. 数据精度配置
DISTANCE_PRECISION = 10     # 距离数据精度倍数(保留1位小数)
SIZE_PRECISION = 10         # 尺寸数据精度倍数(保留1位小数)

# ==================== 配置参数结束 ====================

sensor = None
mono_measure = None  # 单目测量对象
uart_protocol = None  # 串口协议处理器
display_ui = None  # 显示界面对象
measurement_state = "IDLE"  # 测量状态: IDLE, CALIBRATING, MEASURING
last_measurement = None  # 最后测量结果

fpioa = FPIOA()

fpioa.set_function(11, FPIOA.UART2_TXD)
fpioa.set_function(12, FPIOA.UART2_RXD)

button = Pin(43, Pin.IN, Pin.PULL_UP)

uart = UART(UART.UART2, baudrate=UART_BAUDRATE, bits=UART.EIGHTBITS, parity=UART.PARITY_NONE, stop=UART.STOPBITS_ONE)



# 将buf1定义为全局变量
buf1 = b'\x00\x00\x00\x00'  # 初始化buf1为空的数据


# 定时器回调函数，每30ms发送一次数据
def Uart_Task(timer):
    global buf1, last_measurement, uart_protocol, measurement_state, mono_measure, uart

    try:
        # 检查UART对象是否有效
        if uart is None:
            return

        # 发送基础buf1数据（保持兼容性）
        if len(buf1) >= 4:
            hex_str = buf1.hex().upper()
#            print("buf1十六进制内容:", hex_str)
            uart.write(buf1)

        # 发送扩展协议数据
        if uart_protocol:
            # 发送系统状态
            status_frame = uart_protocol.create_status_frame(
                measurement_state.lower(),
                mono_measure.focal_length if mono_measure and mono_measure.calibrated else None
            )
            uart.write(status_frame)

            # 如果有测量结果，发送测量数据
            if last_measurement:
                distance, shape, size = last_measurement
                measurement_frame = uart_protocol.create_measurement_frame(distance, shape, size)
                uart.write(measurement_frame)

#                print(f"发送测量数据: 距离={distance:.1f}cm, 形状={shape}, 尺寸={size:.1f}cm")

    except Exception as e:
        print(f"UART发送异常: {e}")
        # 如果UART出错，可以选择重新初始化或忽略


# 发送协议帧（不包括校验和）
def send_protocol_frame(uart, frame_header, data):
    frame = bytearray()          # 创建字节数组
    frame.append(frame_header)   # 添加任务帧头
    frame.extend(data)           # 添加数据部分
    frame.append(0x55)           # 帧尾

    uart.write(frame)            # 通过串口发送整个帧

# 显示系统状态和测量结果
def display_system_status(img, fps):
    """在图像上显示系统状态和测量结果"""
    global measurement_state, last_measurement, mono_measure, display_ui

    if display_ui is None:
        # 如果显示界面未初始化，使用简单文本显示
        img.draw_string_advanced(50, 50, 25, f"fps: {fps:.1f}", color=(255, 0, 0))
        img.draw_string_advanced(50, 100, 20, f"Status: {measurement_state}", color=(255, 255, 0))
        return

    # 准备系统数据
    system_data = {
        'status': measurement_state.lower(),
        'focal_length': mono_measure.focal_length if mono_measure and mono_measure.calibrated else None,
        'measurement': None,
        'fps': fps
    }

    # 如果有测量结果，转换为显示格式
    if last_measurement:
        distance, shape, size = last_measurement
        system_data['measurement'] = {
            'distance_cm': distance,
            'shape_type': shape,
            'size_cm': size,
            'timestamp': int(time.time() * 1000)  # 当前时间戳
        }

    # 绘制完整的用户界面
    display_ui.draw_complete_ui(img, system_data)

# 主程序
try:
    # 初始化传感器
    sensor = Sensor(width=SENSOR_WIDTH, height=SENSOR_HEIGHT)
    sensor.reset()

    # 设置传感器分辨率
    sensor.set_framesize(width=FRAME_WIDTH, height=FRAME_HEIGHT)
    sensor.set_pixformat(Sensor.RGB565)

    # 初始化显示和媒体管理器 - 使用ST7701驱动2.4寸屏
    Display.init(Display.ST7701, width=FRAME_WIDTH, height=FRAME_HEIGHT, to_ide=True)
    MediaManager.init()
    sensor.run()
    clock = time.clock()

    # 初始化单目测量系统
    mono_measure = MonoMeasure()
    print("单目测量系统初始化完成")

    # 初始化串口协议处理器
    uart_protocol = UartProtocol()
    print("串口协议处理器初始化完成")

    # 初始化显示界面
    display_ui = DisplayUI(screen_width=FRAME_WIDTH, screen_height=FRAME_HEIGHT)
    print("显示界面初始化完成")

    # 设置检测参数
    mono_measure.set_detection_params(
        min_area=MIN_A4_AREA,           # 最小A4纸面积
        max_area=MAX_A4_AREA,           # 最大A4纸面积
        aspect_tolerance=ASPECT_TOLERANCE # 长宽比容差
    )

    # 启动定时器
    tim = Timer(-1)
    tim.init(period=TIMER_PERIOD, mode=Timer.PERIODIC, callback=Uart_Task)

    while True:
        clock.tick()
        os.exitpoint()

        # 获取摄像头图像
        img = sensor.snapshot()

        # 处理按键事件 - 一键测量功能
        if button.value() == 0:
            time.sleep_ms(BUTTON_DEBOUNCE_MS)  # 消抖延时
            if button.value() == 0:
                print("按键按下 - 开始测量")

                # 执行测量流程
                if measurement_state == "IDLE":
                    # 首次按键：进入标定模式
                    print(f"进入标定模式，请将A4纸放置在{CALIBRATION_DISTANCE}cm处")
                    measurement_state = "CALIBRATING"

                    # 执行标定
                    success, focal_length = mono_measure.calibrate(img, CALIBRATION_DISTANCE)
                    if success:
                        print(f"标定成功，焦距: {focal_length:.2f}")
                        measurement_state = "MEASURING"

                        # 在图像上显示标定成功信息
                        img.draw_string_advanced(50, 100, 20, "Calibration OK", color=(0, 255, 0))
                        img.draw_string_advanced(50, 130, 20, f"F={focal_length:.1f}", color=(0, 255, 0))
                    else:
                        print("标定失败，请重新尝试")
                        measurement_state = "IDLE"
                        img.draw_string_advanced(50, 100, 20, "Calibration Failed", color=(255, 0, 0))

                elif measurement_state == "MEASURING":
                    # 后续按键：执行测量
                    print("执行测量...")
                    result = mono_measure.measure(img)

                    if result:
                        distance, shape, size = result
                        last_measurement = result
                        print(f"测量成功: 距离={distance:.1f}cm, 形状={shape}, 尺寸={size:.1f}cm")

                        # 更新buf1用于串口发送（距离和尺寸数据）
                        distance_int = int(distance * DISTANCE_PRECISION)  # 距离精度转换
                        size_int = int(size * SIZE_PRECISION)              # 尺寸精度转换
                        buf1 = struct.pack('<HH', distance_int, size_int)

                    else:
                        print("测量失败")
                        img.draw_string_advanced(50, 100, 20, "Measurement Failed", color=(255, 0, 0))

                # 等待按键释放
                while not button.value():
                    pass

        # 在图像上显示系统状态和测量结果
        display_system_status(img, clock.fps())

        # 发送图像到IDE和显示屏
        img.compressed_for_ide()
        Display.show_image(img)

except KeyboardInterrupt as e:
    print("用户停止: ", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
    uart.deinit()
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
